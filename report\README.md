# Mathematical Modelling Project Report

This directory contains the LaTeX source files for the mathematical modelling project report.

## Structure

```
report/
├── main.tex                    # Main document file
├── preamble.tex               # LaTeX preamble with packages and settings
├── bibliography.bib           # Bibliography file
├── Makefile                   # Build automation
├── README.md                  # This file
└── sections/
    ├── abstract.tex           # Abstract
    ├── introduction.tex       # Introduction and problem statement
    ├── literature_review.tex  # Literature review and background
    ├── methodology.tex        # Modelling methodology
    ├── model_development.tex  # Model development and formulation
    ├── results.tex           # Results and analysis
    ├── discussion.tex        # Discussion and interpretation
    ├── conclusion.tex        # Conclusions and future work
    └── appendices.tex        # Appendices
```

## Compilation

### Using Make (Recommended)

```bash
# Compile the complete document with bibliography
make

# Quick compile without bibliography
make quick

# Clean auxiliary files
make clean

# Clean all files including PDF
make cleanall

# Force rebuild
make rebuild

# View the PDF (Windows)
make view
```

### Manual Compilation

```bash
# Full compilation with bibliography
pdflatex main.tex
bibtex main
pdflatex main.tex
pdflatex main.tex

# Quick compilation
pdflatex main.tex
```

## Requirements

- LaTeX distribution (e.g., TeX Live, MiKTeX)
- Required packages (see preamble.tex for complete list):
  - amsmath, amssymb, amsthm (mathematics)
  - graphicx, tikz, pgfplots (graphics)
  - booktabs, array (tables)
  - hyperref, cite (references)
  - And others...

## Usage Instructions

1. **Customize the content**: Replace placeholder text in each section file with your actual content
2. **Add figures**: Place figure files in a `figures/` subdirectory and reference them in the text
3. **Update bibliography**: Add your references to `bibliography.bib` in BibTeX format
4. **Modify document info**: Update title, author, and date in `main.tex`
5. **Adjust formatting**: Modify `preamble.tex` for custom formatting requirements

## Section Guidelines

### Abstract (150-300 words)
- Problem statement
- Methodology
- Key findings
- Main conclusions

### Introduction
- Background and context
- Problem statement and research questions
- Objectives
- Scope and limitations
- Report structure

### Literature Review
- Theoretical background
- Existing models and approaches
- Previous studies
- Research gaps
- Contribution of this work

### Methodology
- Modelling framework
- Key assumptions
- Mathematical formulation strategy
- Solution methods
- Validation approach

### Model Development
- Problem formulation
- Variable and parameter definitions
- Mathematical model
- Model analysis
- Solution approach

### Results
- Model implementation
- Baseline results
- Sensitivity analysis
- Model validation
- Scenario analysis

### Discussion
- Interpretation of results
- Comparison with literature
- Model performance and validity
- Practical implications
- Recommendations

### Conclusion
- Summary of work
- Key findings
- Achievement of objectives
- Contributions
- Limitations and future work

### Appendices
- Mathematical derivations
- Numerical methods and algorithms
- Code implementation
- Additional results
- Data and parameters

## Tips

- Use `\label{}` and `\ref{}` for cross-references
- Include figures with `\includegraphics{}` and proper captions
- Use `\cite{}` for citations
- Number equations with `\begin{align}...\end{align}`
- Use `\begin{table}` and `\begin{figure}` environments with `[H]` for positioning
- Keep sections modular for easier editing and collaboration

## Common Issues

- **Missing packages**: Install required LaTeX packages through your distribution's package manager
- **Bibliography not appearing**: Run the full compilation sequence (pdflatex → bibtex → pdflatex → pdflatex)
- **Figures not found**: Check file paths and ensure figures are in the correct directory
- **Cross-references showing ??**: Run pdflatex multiple times to resolve references
