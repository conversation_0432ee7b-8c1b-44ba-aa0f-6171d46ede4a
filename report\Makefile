# Makefile for LaTeX report compilation

# Main document name (without .tex extension)
MAIN = main

# LaTeX compiler
LATEX = pdflatex

# BibTeX compiler
BIBTEX = bibtex

# Default target
all: $(MAIN).pdf

# Compile the main document
$(MAIN).pdf: $(MAIN).tex preamble.tex sections/*.tex bibliography.bib
	$(LATEX) $(MAIN).tex
	$(BIBTEX) $(MAIN)
	$(LATEX) $(MAIN).tex
	$(LATEX) $(MAIN).tex

# Quick compile (without bibliography)
quick: $(MAIN).tex preamble.tex sections/*.tex
	$(LATEX) $(MAIN).tex

# Clean auxiliary files
clean:
	rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fdb_latexmk *.fls *.synctex.gz

# Clean all generated files including PDF
cleanall: clean
	rm -f $(MAIN).pdf

# Force rebuild
rebuild: cleanall all

# View the PDF (adjust viewer as needed)
view: $(MAIN).pdf
	start $(MAIN).pdf

.PHONY: all quick clean cleanall rebuild view
