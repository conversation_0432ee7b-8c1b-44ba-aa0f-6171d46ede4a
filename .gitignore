# Python-generated files
__pycache__/
*.py[oc]
build/
dist/
wheels/
*.egg-info

# Virtual environments
.venv
venv/
env/
ENV/

# Jupyter Notebook
.ipynb_checkpoints/
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VSCode
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# LaTeX auxiliary files
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.log
*.out
*.synctex.gz
*.toc
*.lof
*.lot
*.acn
*.acr
*.alg
*.glg
*.glo
*.gls
*.ist
*.loa
*.nav
*.snm
*.vrb
*.xdy
*.figlist
*.makefile
*.fls_latexmk

# LaTeX output (comment out if you want to include PDFs)
*.pdf

# BibTeX
*.bib.bak
*.bib.sav

# Data files (uncomment if you want to exclude large datasets)
# *.csv
# *.xlsx
# *.json
# *.parquet

# Model files
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5

# Plots and figures
plots/
figures/
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.eps

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Desktop.ini

# Linux
*~

# Logs
*.log
logs/

# Cache
.cache/
*.cache

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django
*.log
local_settings.py
db.sqlite3

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook outputs (uncomment if you want to exclude outputs)
# *.ipynb

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Pytest
.pytest_cache/

# Benchmarks
.benchmarks/

# Profiling
*.prof

# Machine learning experiments
experiments/
runs/
wandb/
mlruns/

# Tensorboard logs
logs/
tensorboard/

# Weights & Biases
wandb/

# MLflow
mlruns/

# DVC
.dvc/
.dvcignore

# Hydra
outputs/
multirun/

# Ray
ray_results/

# Optuna
optuna_studies/

# Config files with secrets
config.ini
secrets.yaml
.secrets

# Database
*.db
*.sqlite
*.sqlite3

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z
