% Bibliography file for the modelling project report
% Add your references here in BibTeX format

% Example book reference
@book{example_book,
    author = {Author, A. and Author, B.},
    title = {Title of the Book},
    publisher = {Publisher Name},
    year = {2023},
    address = {City, Country},
    edition = {2nd}
}

% Example journal article reference
@article{example_article,
    author = {<PERSON>, <PERSON> and <PERSON>, <PERSON>},
    title = {A Mathematical Model for Complex Systems},
    journal = {Journal of Mathematical Modelling},
    volume = {45},
    number = {3},
    pages = {123--145},
    year = {2023},
    doi = {10.1000/example.doi}
}

% Example conference paper reference
@inproceedings{example_conference,
    author = {<PERSON>, K<PERSON> and <PERSON>, L.},
    title = {Numerical Methods for Differential Equations},
    booktitle = {Proceedings of the International Conference on Computational Mathematics},
    pages = {67--78},
    year = {2022},
    address = {New York, USA},
    publisher = {ACM Press}
}

% Example thesis reference
@phdthesis{example_thesis,
    author = {<PERSON>, R<PERSON>},
    title = {Advanced Techniques in Mathematical Modelling},
    school = {University of Example},
    year = {2021},
    address = {City, Country}
}

% Example online resource
@misc{example_online,
    author = {Organization, X.},
    title = {Online Resource Title},
    howpublished = {\url{https://www.example.com/resource}},
    note = {Accessed: 2023-12-01},
    year = {2023}
}

% Example software reference
@misc{python,
    author = {{Python Software Foundation}},
    title = {Python Programming Language},
    howpublished = {\url{https://www.python.org/}},
    year = {2023}
}

@misc{numpy,
    author = {Harris, Charles R. and others},
    title = {Array programming with {NumPy}},
    journal = {Nature},
    volume = {585},
    pages = {357--362},
    year = {2020},
    doi = {10.1038/s41586-020-2649-2}
}

@misc{matplotlib,
    author = {Hunter, J. D.},
    title = {Matplotlib: A 2D graphics environment},
    journal = {Computing in Science \& Engineering},
    volume = {9},
    number = {3},
    pages = {90--95},
    year = {2007},
    doi = {10.1109/MCSE.2007.55}
}

@misc{scipy,
    author = {Virtanen, Pauli and others},
    title = {{SciPy} 1.0: Fundamental Algorithms for Scientific Computing in Python},
    journal = {Nature Methods},
    volume = {17},
    pages = {261--272},
    year = {2020},
    doi = {10.1038/s41592-019-0686-2}
}
